{"name": "divvy-degiro-mcp-server", "version": "1.0.0", "description": "MCP server for finding DEGIRO-available dividend opportunities from DivvyDiary", "type": "module", "main": "divvy-degiro-mcp-server.js", "bin": {"divvy-degiro-mcp-server": "./divvy-degiro-mcp-server.js"}, "scripts": {"start": "node divvy-degiro-mcp-server.js", "dev": "node --inspect divvy-degiro-mcp-server.js", "test": "node test-server.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12"}, "devDependencies": {"@types/node": "^20.0.0"}, "keywords": ["mcp", "dividends", "de<PERSON>ro", "investing", "finance"], "author": "Dividend Beast Hunter", "license": "MIT", "engines": {"node": ">=18.0.0"}}