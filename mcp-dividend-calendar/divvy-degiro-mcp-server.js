#!/usr/bin/env node

/**
 * DivvyDiary + DEGIRO MCP Server
 * Simple MCP server for finding dividend beasts available on DEGIRO
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import axios from 'axios';
import * as cheerio from 'cheerio';

class DivvyDegiroMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'divvy-degiro-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // DEGIRO supported exchanges and known restrictions
    this.degiroExchanges = {
      available: ['LSE', 'XETRA', 'Euronext', 'Oslo Børs', 'Borsa Italiana', 'BME', 'WSE', 'SIX'],
      blocked: ['JSE', 'ASX', 'TSX', 'TWSE', 'NSE', 'B3', 'Moscow Exchange'],
      usEtfBlocked: true, // PRIIPs regulation blocks US ETFs
    };

    // Cache for DEGIRO availability checks
    this.availabilityCache = new Map();

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'scan_dividend_calendar',
          description: 'Scan DivvyDiary dividend calendar for DEGIRO-available dividend beasts',
          inputSchema: {
            type: 'object',
            properties: {
              startDate: { type: 'string', description: 'Start date (YYYY-MM-DD)' },
              endDate: { type: 'string', description: 'End date (YYYY-MM-DD)' },
              minYield: { type: 'number', description: 'Minimum yield percentage (default: 5)' },
              minDividend: { type: 'number', description: 'Minimum dividend amount in EUR (default: 0.5)' },
              maxResults: { type: 'number', description: 'Maximum results to return (default: 20)' }
            },
            required: ['startDate', 'endDate']
          }
        },
        {
          name: 'verify_degiro_availability',
          description: 'Verify if specific securities are available on DEGIRO',
          inputSchema: {
            type: 'object',
            properties: {
              securities: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    ticker: { type: 'string' },
                    isin: { type: 'string' },
                    exchange: { type: 'string' }
                  }
                }
              }
            },
            required: ['securities']
          }
        },
        {
          name: 'find_dividend_beasts',
          description: 'Find the highest yield dividend opportunities available on DEGIRO',
          inputSchema: {
            type: 'object',
            properties: {
              dateRange: { type: 'string', description: 'Date range: today, week, month, quarter' },
              sortBy: { type: 'string', enum: ['yield', 'amount', 'date'], description: 'Sort criteria' },
              filterEuropean: { type: 'boolean', description: 'Filter only European stocks' }
            }
          }
        }
      ]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'scan_dividend_calendar':
            return await this.scanDividendCalendar(args);
          case 'verify_degiro_availability':
            return await this.verifyDegiroAvailability(args);
          case 'find_dividend_beasts':
            return await this.findDividendBeasts(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`
            }
          ]
        };
      }
    });
  }

  async scanDividendCalendar({ startDate, endDate, minYield = 5, minDividend = 0.5, maxResults = 20 }) {
    const dividends = [];
    const dateRange = this.getDateRange(startDate, endDate);

    for (const date of dateRange) {
      try {
        const dayDividends = await this.scrapeDivvyDiaryDay(date);
        dividends.push(...dayDividends);
      } catch (error) {
        console.error(`Failed to scrape ${date}:`, error.message);
      }
    }

    // Filter by criteria and DEGIRO availability
    const filtered = await this.filterDividends(dividends, minYield, minDividend);
    const sorted = filtered.sort((a, b) => b.yield - a.yield).slice(0, maxResults);

    return {
      content: [
        {
          type: 'text',
          text: this.formatDividendResults(sorted, 'Dividend Calendar Scan Results')
        }
      ]
    };
  }

  async scrapeDivvyDiaryDay(date) {
    const url = `https://divvydiary.com/en/calendar/${date}`;
    
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const dividends = [];

      $('table tbody tr').each((i, row) => {
        const cells = $(row).find('td');
        if (cells.length >= 5) {
          const name = $(cells[0]).find('a').text().trim();
          const exDate = $(cells[1]).text().trim();
          const payDate = $(cells[2]).text().trim();
          const yieldText = $(cells[3]).text().trim();
          const amountText = $(cells[4]).text().trim();

          if (name && yieldText && amountText) {
            const yield = parseFloat(yieldText.replace('%', ''));
            const amount = this.parseAmount(amountText);
            
            if (!isNaN(yield) && amount > 0) {
              dividends.push({
                name,
                exDate,
                payDate,
                yield,
                amount: amount.value,
                currency: amount.currency,
                amountEur: this.convertToEur(amount.value, amount.currency),
                exchange: this.guessExchange(name),
                url: $(cells[0]).find('a').attr('href')
              });
            }
          }
        }
      });

      return dividends;
    } catch (error) {
      throw new Error(`Failed to scrape DivvyDiary for ${date}: ${error.message}`);
    }
  }

  async filterDividends(dividends, minYield, minDividend) {
    const filtered = [];

    for (const dividend of dividends) {
      // Basic filters
      if (dividend.yield < minYield || dividend.amountEur < minDividend) {
        continue;
      }

      // Check DEGIRO availability
      const isAvailable = await this.checkDegiroAvailability(dividend);
      if (isAvailable) {
        dividend.degiroAvailable = true;
        dividend.degiroStatus = 'Available';
        filtered.push(dividend);
      } else {
        dividend.degiroAvailable = false;
        dividend.degiroStatus = 'Blocked/Not Available';
      }
    }

    return filtered;
  }

  async checkDegiroAvailability(security) {
    const cacheKey = `${security.name}_${security.exchange}`;
    
    if (this.availabilityCache.has(cacheKey)) {
      return this.availabilityCache.get(cacheKey);
    }

    let available = false;

    // Quick exchange-based filtering
    if (this.degiroExchanges.blocked.includes(security.exchange)) {
      available = false;
    } else if (this.degiroExchanges.available.includes(security.exchange)) {
      available = true;
    } else if (security.name.includes('ETF') && security.exchange === 'US') {
      available = false; // US ETFs blocked by PRIIPs
    } else {
      // For ambiguous cases, assume available if European
      available = this.isEuropeanSecurity(security);
    }

    this.availabilityCache.set(cacheKey, available);
    return available;
  }

  isEuropeanSecurity(security) {
    const europeanKeywords = ['PLC', 'SA', 'SpA', 'AG', 'NV', 'ASA', 'AB'];
    const europeanCountries = ['UK', 'Germany', 'France', 'Italy', 'Spain', 'Netherlands', 'Belgium', 'Norway'];
    
    return europeanKeywords.some(keyword => security.name.includes(keyword)) ||
           europeanCountries.some(country => security.exchange?.includes(country));
  }

  guessExchange(companyName) {
    if (companyName.includes('PLC')) return 'LSE';
    if (companyName.includes('AG')) return 'XETRA';
    if (companyName.includes('SA')) return 'Euronext';
    if (companyName.includes('SpA')) return 'Borsa Italiana';
    if (companyName.includes('ASA')) return 'Oslo Børs';
    if (companyName.includes('NV')) return 'Euronext';
    if (companyName.includes('ETF')) return 'US';
    return 'Unknown';
  }

  parseAmount(amountText) {
    const match = amountText.match(/([A-Z$£€¥₹]+)\s*([\d.,]+)/);
    if (match) {
      const currency = match[1];
      const value = parseFloat(match[2].replace(/,/g, ''));
      return { currency, value };
    }
    return { currency: 'EUR', value: 0 };
  }

  convertToEur(amount, currency) {
    // Simplified conversion rates (in practice, use real-time rates)
    const rates = {
      'EUR': 1, '€': 1,
      'USD': 0.85, '$': 0.85,
      'GBP': 1.15, '£': 1.15,
      'NOK': 0.085,
      'PLN': 0.23,
      'SEK': 0.088
    };
    return amount * (rates[currency] || 0.85);
  }

  getDateRange(startDate, endDate) {
    const dates = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      dates.push(d.toISOString().split('T')[0]);
    }
    
    return dates;
  }

  formatDividendResults(dividends, title) {
    let result = `# ${title}\n\n`;
    
    if (dividends.length === 0) {
      return result + 'No dividend opportunities found matching your criteria.';
    }

    result += `Found ${dividends.length} dividend beasts available on DEGIRO:\n\n`;
    
    dividends.forEach((div, i) => {
      result += `## ${i + 1}. ${div.name}\n`;
      result += `- **Yield**: ${div.yield.toFixed(2)}%\n`;
      result += `- **Dividend**: ${div.currency}${div.amount} (≈€${div.amountEur.toFixed(2)})\n`;
      result += `- **Pay Date**: ${div.payDate}\n`;
      result += `- **Exchange**: ${div.exchange}\n`;
      result += `- **DEGIRO**: ✅ Available\n\n`;
    });

    return result;
  }

  async verifyDegiroAvailability({ securities }) {
    const results = [];
    
    for (const security of securities) {
      const available = await this.checkDegiroAvailability(security);
      results.push({
        ...security,
        degiroAvailable: available,
        status: available ? 'Available' : 'Blocked/Not Available'
      });
    }

    return {
      content: [
        {
          type: 'text',
          text: this.formatAvailabilityResults(results)
        }
      ]
    };
  }

  formatAvailabilityResults(results) {
    let output = '# DEGIRO Availability Check\n\n';
    
    results.forEach((result, i) => {
      const status = result.degiroAvailable ? '✅ Available' : '❌ Not Available';
      output += `${i + 1}. **${result.name}** (${result.exchange}): ${status}\n`;
    });
    
    return output;
  }

  async findDividendBeasts({ dateRange = 'month', sortBy = 'yield', filterEuropean = true }) {
    const today = new Date();
    let endDate = new Date(today);
    
    switch (dateRange) {
      case 'week':
        endDate.setDate(today.getDate() + 7);
        break;
      case 'month':
        endDate.setMonth(today.getMonth() + 1);
        break;
      case 'quarter':
        endDate.setMonth(today.getMonth() + 3);
        break;
      default:
        endDate.setDate(today.getDate() + 1);
    }

    return await this.scanDividendCalendar({
      startDate: today.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      minYield: filterEuropean ? 3 : 5,
      minDividend: 0.1,
      maxResults: 15
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('DivvyDiary + DEGIRO MCP Server running on stdio');
  }
}

// Run the server
const server = new DivvyDegiroMCPServer();
server.run().catch(console.error);
