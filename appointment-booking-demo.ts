#!/usr/bin/env bun

/**
 * Beauty CRM Appointment Booking Demonstration
 *
 * This script demonstrates how appointment booking works in the Beauty CRM system.
 * Since the services are currently building in Tilt, this shows the expected functionality.
 */

interface CreateAppointmentRequest {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  salonId: string;
  staffId: string;
  treatmentId: string;
  appointmentDate: string;
  appointmentTime: string;
  duration: number;
  notes?: string;
}

interface Appointment {
  id: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  salonId: string;
  staffId: string;
  treatmentId: string;
  appointmentDate: string;
  appointmentTime: string;
  duration: number;
  status: 'SCHEDULED' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

class AppointmentBookingDemo {
  private appointments: Appointment[] = [];

  /**
   * Simulates booking an appointment through the Beauty CRM system
   */
  async bookAppointment(
    request: CreateAppointmentRequest,
  ): Promise<Appointment> {
    console.log('🎯 Booking appointment...');
    console.log('📋 Request details:', JSON.stringify(request, null, 2));

    // Simulate validation
    this.validateAppointmentRequest(request);

    // Simulate creating appointment
    const appointment: Appointment = {
      id: `apt_${Date.now()}`,
      ...request,
      status: 'SCHEDULED',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Simulate saving to database
    this.appointments.push(appointment);

    // Simulate event publishing (would use NATS in real system)
    await this.publishAppointmentEvent('appointment.created', appointment);

    console.log('✅ Appointment booked successfully!');
    console.log(
      '📅 Appointment details:',
      JSON.stringify(appointment, null, 2),
    );

    return appointment;
  }

  /**
   * Simulates retrieving appointments for a salon
   */
  async getAppointmentsBySalon(salonId: string): Promise<Appointment[]> {
    console.log(`🏪 Retrieving appointments for salon: ${salonId}`);

    const salonAppointments = this.appointments.filter(
      (apt) => apt.salonId === salonId,
    );

    console.log(`📊 Found ${salonAppointments.length} appointments`);
    return salonAppointments;
  }

  /**
   * Simulates retrieving appointments for a specific date
   */
  async getAppointmentsByDate(
    salonId: string,
    date: string,
  ): Promise<Appointment[]> {
    console.log(`📅 Retrieving appointments for salon ${salonId} on ${date}`);

    const dateAppointments = this.appointments.filter(
      (apt) => apt.salonId === salonId && apt.appointmentDate === date,
    );

    console.log(`📊 Found ${dateAppointments.length} appointments for ${date}`);
    return dateAppointments;
  }

  private validateAppointmentRequest(request: CreateAppointmentRequest): void {
    if (!request.clientName) throw new Error('Client name is required');
    if (!request.clientEmail) throw new Error('Client email is required');
    if (!request.salonId) throw new Error('Salon ID is required');
    if (!request.staffId) throw new Error('Staff ID is required');
    if (!request.treatmentId) throw new Error('Treatment ID is required');
    if (!request.appointmentDate)
      throw new Error('Appointment date is required');
    if (!request.appointmentTime)
      throw new Error('Appointment time is required');
    if (!request.duration || request.duration <= 0)
      throw new Error('Valid duration is required');

    console.log('✅ Appointment request validation passed');
  }

  private async publishAppointmentEvent(
    eventType: string,
    appointment: Appointment,
  ): Promise<void> {
    console.log(`📡 Publishing event: ${eventType}`);
    console.log('🔄 Event would be sent via NATS to notify other services');

    // In the real system, this would:
    // 1. Send notification to client (email/SMS)
    // 2. Update salon calendar
    // 3. Notify staff member
    // 4. Update analytics

    console.log('✅ Event published successfully');
  }

  /**
   * Display current appointments in a nice format
   */
  displayAppointments(): void {
    console.log('\n📋 Current Appointments:');
    console.log('='.repeat(80));

    if (this.appointments.length === 0) {
      console.log('No appointments found.');
      return;
    }

    this.appointments.forEach((apt, index) => {
      console.log(`\n${index + 1}. Appointment ID: ${apt.id}`);
      console.log(`   Client: ${apt.clientName} (${apt.clientEmail})`);
      console.log(`   Date: ${apt.appointmentDate} at ${apt.appointmentTime}`);
      console.log(`   Duration: ${apt.duration} minutes`);
      console.log(`   Status: ${apt.status}`);
      console.log(`   Salon: ${apt.salonId}`);
      console.log(`   Staff: ${apt.staffId}`);
      console.log(`   Treatment: ${apt.treatmentId}`);
      if (apt.notes) console.log(`   Notes: ${apt.notes}`);
    });
  }
}

// Demo execution
async function runDemo() {
  console.log('🌟 Beauty CRM Appointment Booking Demo');
  console.log('='.repeat(50));

  const demo = new AppointmentBookingDemo();

  try {
    // Demo appointment 1: Hair cut
    await demo.bookAppointment({
      clientName: 'Alice Johnson',
      clientEmail: '<EMAIL>',
      clientPhone: '******-0123',
      salonId: 'salon_downtown',
      staffId: 'staff_sarah',
      treatmentId: 'treatment_haircut',
      appointmentDate: '2025-01-25',
      appointmentTime: '10:00',
      duration: 60,
      notes: 'First time client, prefers shorter style',
    });

    // Demo appointment 2: Manicure
    await demo.bookAppointment({
      clientName: 'Bob Smith',
      clientEmail: '<EMAIL>',
      clientPhone: '******-0456',
      salonId: 'salon_downtown',
      staffId: 'staff_maria',
      treatmentId: 'treatment_manicure',
      appointmentDate: '2025-01-25',
      appointmentTime: '14:30',
      duration: 45,
      notes: 'Regular client, prefers gel polish',
    });

    // Demo appointment 3: Facial
    await demo.bookAppointment({
      clientName: 'Carol Davis',
      clientEmail: '<EMAIL>',
      clientPhone: '******-0789',
      salonId: 'salon_uptown',
      staffId: 'staff_jenny',
      treatmentId: 'treatment_facial',
      appointmentDate: '2025-01-26',
      appointmentTime: '11:00',
      duration: 90,
      notes: 'Sensitive skin, avoid fragranced products',
    });

    // Display all appointments
    demo.displayAppointments();

    // Demo queries
    console.log('\n🔍 Demo Queries:');
    console.log('='.repeat(30));

    const downtownAppointments =
      await demo.getAppointmentsBySalon('salon_downtown');
    console.log(
      `\n📊 Downtown salon has ${downtownAppointments.length} appointments`,
    );

    const jan25Appointments = await demo.getAppointmentsByDate(
      'salon_downtown',
      '2025-01-25',
    );
    console.log(
      `📊 Downtown salon has ${jan25Appointments.length} appointments on 2025-01-25`,
    );

    console.log('\n✨ Demo completed successfully!');
    console.log(
      '\n📝 Note: This demonstrates the appointment booking functionality.',
    );
    console.log(
      '🔧 The actual services are currently building in Tilt and will be available soon.',
    );
  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

// Run the demo
runDemo();
