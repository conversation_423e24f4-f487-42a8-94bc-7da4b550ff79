{"mcpServers": {"beauty_crm_appointment": {"args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_appointment"], "command": "npx"}, "beauty_crm_planner": {"args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_planner"], "command": "npx"}, "context7": {"args": ["-y", "@upstash/context7-mcp"], "command": "npx"}, "Prisma-Local": {"args": ["-y", "prisma", "mcp"], "command": "npx"}, "playwright": {"args": ["-y", "@playwright/mcp"], "command": "npx"}, "playwright-mcp-server": {"_args": ["-y", "@executeautomation/playwright-mcp-server"], "_command": "npx"}, "dividend-calendar": {"command": "node", "args": ["/private/var/www/2025/ollamar1/beauty-crm/mcp-dividend-calendar/divvy-degiro-mcp-server.js"], "env": {"NODE_ENV": "production"}}}}